
import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import HomePage from './pages/HomePage';
import StoryPage from './pages/StoryPage';
import NftPage from './pages/NftPage';
import EducationPage from './pages/EducationPage'; // Ensured relative path
import CommunityPage from './pages/CommunityPage'; // Ensured relative path

const App: React.FC = () => {
  const location = useLocation();

  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-billieBlack via-neutral-800 to-purple-900">
      <Navbar />
      <main className="flex-grow">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/story" element={<StoryPage />} />
          <Route path="/nfts" element={<NftPage />} />
          <Route path="/education" element={<EducationPage />} />
          <Route path="/community" element={<CommunityPage />} />
        </Routes>
      </main>
      <Footer />
    </div>
  );
};

export default App;

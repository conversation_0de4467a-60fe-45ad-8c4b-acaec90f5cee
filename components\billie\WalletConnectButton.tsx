
import React, { useState } from 'react';
import Button from '../ui/Button';
import { WalletIcon, ArrowPathIcon } from '../ui/Icons'; // Assuming Icons.tsx
import Modal from '../ui/Modal';

const WalletConnectButton: React.FC = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    setIsModalOpen(false); // Close modal if it was open

    // Simulate wallet connection
    // In a real app, you'd use libraries like ethers.js, web3modal, wagmi, etc.
    // For example:
    // if (window.ethereum) {
    //   try {
    //     const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
    //     setWalletAddress(accounts[0]);
    //     setIsConnected(true);
    //   } catch (error) {
    //     console.error("Wallet connection failed:", error);
    //     alert("Failed to connect wallet. See console for details.");
    //   }
    // } else {
    //   alert("MetaMask (or other Ethereum wallet) not detected. Please install it.");
    // }
    
    setTimeout(() => {
      const mockAddress = "0x" + Array(40).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('');
      setWalletAddress(mockAddress);
      setIsConnected(true);
      setIsConnecting(false);
    }, 1500);
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setWalletAddress(null);
  };

  const displayAddress = walletAddress 
    ? `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}`
    : '';

  if (isConnected && walletAddress) {
    return (
      <Button variant="outline" onClick={handleDisconnect} size="md">
        {displayAddress}
      </Button>
    );
  }

  return (
    <>
      <Button 
        variant="primary" 
        onClick={() => setIsModalOpen(true)} 
        disabled={isConnecting}
        leftIcon={isConnecting ? <ArrowPathIcon className="w-5 h-5 animate-spin" /> : <WalletIcon className="w-5 h-5" />}
      >
        {isConnecting ? 'Connecting...' : 'Connect Wallet'}
      </Button>
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} title="Connect Wallet">
        <div className="space-y-4 p-2">
            <p className="text-sm text-neutral-300">
                Connect your wallet to interact with Billie's Web3 features, like minting NFTs.
            </p>
            <Button 
              variant="primary" 
              className="w-full"
              onClick={handleConnect}
              disabled={isConnecting}
              leftIcon={isConnecting ? <ArrowPathIcon className="w-5 h-5 animate-spin" /> : <WalletIcon className="w-5 h-5" />}
            >
              {isConnecting ? 'Connecting...' : 'Connect with MetaMask (Mock)'}
            </Button>
             <Button 
              variant="secondary" 
              className="w-full"
              onClick={handleConnect} // Mock: all buttons do same action
              disabled={isConnecting}
            >
              Connect with WalletConnect (Mock)
            </Button>
            <p className="text-xs text-neutral-400 text-center">
                By connecting, you agree to our Terms of Service.
            </p>
        </div>
      </Modal>
    </>
  );
};

export default WalletConnectButton;
    

import React, { useState } from 'react';

// Mock data for interactive elements on Billie
const billieFeatures = [
  { id: 'sunglasses', name: 'Cool Shades', description: 'Purple-tinted, always stylish. Sees through the FUD.', position: { top: '20%', left: '45%' } },
  { id: 'suit', name: 'Power Suit', description: 'Black, sharp, and ready for business in the digital world.', position: { top: '50%', left: '50%' } },
  { id: 'shoes', name: 'Red Kicks', description: 'Fast shoes for outpacing bear markets.', position: { top: '80%', left: '55%' } },
];

const Billie3DModelPlaceholder: React.FC = () => {
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;
    const deltaX = e.clientX - lastMousePos.x;
    const deltaY = e.clientY - lastMousePos.y;
    setRotation(prev => ({
      y: prev.y + deltaX * 0.5, // Adjust sensitivity
      x: Math.max(-30, Math.min(30, prev.x - deltaY * 0.5)) // Clamp X rotation
    }));
    setLastMousePos({ x: e.clientX, y: e.clientY });
  };
  
  // Comment:
  // In a real Three.js implementation, you would:
  // 1. Import Three.js and GLTFLoader.
  // 2. Use a useEffect hook to initialize the scene, camera, renderer, and load the GLB model.
  // 3. Attach event listeners for mouse/touch events to control the camera or model rotation.
  // 4. Implement raycasting for hover interactions on specific parts of the model.
  // For now, this component simulates these interactions with a static image and CSS.

  return (
    <div 
      className="relative w-full max-w-lg aspect-square mx-auto cursor-grab active:cursor-grabbing"
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseUp} // Stop dragging if mouse leaves area
    >
      <div 
        className="w-full h-full bg-gradient-to-br from-purple-500/20 via-billieGold/20 to-red-500/20 rounded-full flex items-center justify-center animate-float"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) translateZ(0px)`, // Added translateZ for perspective hint
          transition: isDragging ? 'none' : 'transform 0.1s ease-out'
        }}
      >
        <img 
          src="https://picsum.photos/seed/billiecharacter/500/500" // Placeholder for Billie's 3D model render
          alt="Billie the Bear - 3D Model Placeholder" 
          className="w-full h-full object-contain p-4 md:p-8 " // Ensure image scales well
        />
      </div>

      {/* Interactive Feature Tooltips (Mock) */}
      {billieFeatures.map(feature => (
        <div
          key={feature.id}
          className="absolute p-1 bg-black/50 rounded-full transform -translate-x-1/2 -translate-y-1/2"
          style={{ top: feature.position.top, left: feature.position.left }}
          onMouseEnter={() => setHoveredFeature(feature.name)}
          onMouseLeave={() => setHoveredFeature(null)}
        >
          <div className="w-3 h-3 bg-billieGold rounded-full animate-subtle-pulse cursor-pointer"></div>
          {hoveredFeature === feature.name && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-billieBlack text-white text-xs rounded shadow-lg whitespace-nowrap z-10">
              <h4 className="font-bold text-billieGold">{feature.name}</h4>
              <p>{feature.description}</p>
            </div>
          )}
        </div>
      ))}
       <p className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs text-neutral-400">
        Click and drag to rotate Billie! (Placeholder)
      </p>
    </div>
  );
};

export default Billie3DModelPlaceholder;
    

import React from 'react';
import { Link } from 'react-router-dom';
import { SOCIAL_LINKS } from '../../constants';

const Footer: React.FC = () => {
  return (
    <footer className="bg-neutral-900 border-t border-neutral-700">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h5 className="font-display text-xl font-bold text-billieGold mb-4">Billie The Bear</h5>
            <p className="text-sm text-neutral-400">
              Exploring the frontiers of Web3. Join the movement.
            </p>
            <p className="text-xs text-neutral-500 mt-2">&copy; {new Date().getFullYear()} Billie The Bear. All rights reserved.</p>
          </div>
          <div>
            <h5 className="text-lg font-semibold text-billieHeading mb-4">Quick Links</h5>
            <ul className="space-y-2">
              <li><Link to="/" className="text-sm text-neutral-400 hover:text-billiePurple transition-colors">Home</Link></li>
              <li><Link to="/story" className="text-sm text-neutral-400 hover:text-billiePurple transition-colors">Billie's Story</Link></li>
              <li><Link to="/nfts" className="text-sm text-neutral-400 hover:text-billiePurple transition-colors">NFTs</Link></li>
              <li><Link to="/education" className="text-sm text-neutral-400 hover:text-billiePurple transition-colors">Web3 Academy</Link></li>
            </ul>
          </div>
          <div>
            <h5 className="text-lg font-semibold text-billieHeading mb-4">Connect</h5>
            <div className="flex space-x-4 mb-4">
              {SOCIAL_LINKS.map(link => (
                <a key={link.name} href={link.url} target="_blank" rel="noopener noreferrer" className="text-neutral-400 hover:text-billiePurple transition-colors">
                  {link.icon}
                  <span className="sr-only">{link.name}</span>
                </a>
              ))}
            </div>
            <h5 className="text-lg font-semibold text-billieHeading mb-2 mt-6">Newsletter</h5>
            <form className="flex flex-col sm:flex-row gap-2">
              <input type="email" placeholder="<EMAIL>" className="w-full px-3 py-2 rounded-md bg-neutral-800 text-billieBodyText border border-neutral-700 focus:ring-2 focus:ring-billiePurple focus:border-transparent outline-none" />
              <button type="submit" className="bg-billiePurple hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-md transition-colors whitespace-nowrap">Subscribe</button>
            </form>
          </div>
        </div>
        <div className="mt-8 pt-8 border-t border-neutral-700 text-center text-xs text-neutral-500">
          <p>Crypto investments are subject to market risks. DYOR.</p>
          <div className="space-x-4 mt-2">
            <Link to="/terms" className="hover:text-billiePurple">Terms of Service</Link>
            <span>|</span>
            <Link to="/privacy" className="hover:text-billiePurple">Privacy Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
    
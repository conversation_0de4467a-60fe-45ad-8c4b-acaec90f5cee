
import React from 'react';

// Basic SVG props
interface SVGProps extends React.SVGProps<SVGSVGElement> {}

export const Bars3Icon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
  </svg>
);

export const XMarkIcon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export const ChevronRightIcon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
  </svg>
);

export const ArrowPathIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
    </svg>
);

export const SparklesIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09l2.846.813-.813 2.846a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 14.188l-1.25.813a4.5 4.5 0 01-3.09-3.09L11.25 10l1.25-.813a4.5 4.5 0 013.09-3.09L17 3.75l1.25.813a4.5 4.5 0 013.09 3.09L22.75 10l-1.25.813a4.5 4.5 0 01-3.09 3.09z" />
    </svg>
);

export const PuzzlePieceIcon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.25 6.083c-.568-.906-1.656-1.458-2.833-1.458s-2.265.552-2.833 1.458m5.666 0c.568.906 1.656 1.458 2.833 1.458s2.265-.552 2.833-1.458m-11.332 0c.568.906 1.656 1.458 2.833 1.458S9.167 5.177 8.599 6.083M5.766 10.042c-.568.906-1.656 1.458-2.833 1.458S.667 11.499.1 10.542m5.666 0c.568.906.438 2.333-.344 3.042s-1.896.688-2.833-.063m11.332-3.042c-.568.906-1.656 1.458-2.833 1.458s-2.265-.552-2.833-1.458m5.666 0c.568.906.438 2.333-.344 3.042s-1.896.688-2.833-.063m0 5.624c.568.906 1.656 1.458 2.833 1.458s2.265-.552 2.833-1.458M8.599 17.167c.568.906 1.656 1.458 2.833 1.458s2.265-.552 2.833-1.458m-5.666 0c-.568-.906-.438-2.333.344-3.042s1.896-.688 2.833.063M19.9 10.542c.568.906 1.656 1.458 2.833 1.458s2.265-.552 2.833-1.458m-5.666 0c-.568.906-.438 2.333.344 3.042s1.896-.688 2.833-.063" />
  </svg>
);


export const BookOpenIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
    </svg>
);

export const LightBulbIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 001.5-.189m-1.5.189a6.01 6.01 0 01-1.5-.189m3.75 7.478a12.06 12.06 0 01-4.5 0m3.75 2.354a15.055 15.055 0 01-4.5 0M12 6.75A2.25 2.25 0 009.75 9v1.076c0 .24.04.473.116.694l.178.533m5.952-.227a2.25 2.25 0 01-2.25 2.25H12v-1.076c0-.24-.04-.473-.116-.694l-.178-.533M12 6.75A2.25 2.25 0 0114.25 9v1.076c0 .24-.04.473-.116.694l-.178-.533m9-3.75a.75.75 0 01.14.53l-.178 5.477a2.25 2.25 0 01-2.236 2.063H6.116A2.25 2.25 0 013.88 12.29l-.178-5.477a.75.75 0 01.14-.531m18 0L12 3M3 6.75L12 3" />
    </svg>
);

export const LinkIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
    </svg>
);

export const CpuChipIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M8.25 21v-1.5M4.5 15.75H3m18 0h-1.5M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5.25-4.5L9.75 12l6 4.5" />
    </svg>
);


export const ScaleIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v17.25m0 0c-1.472 0-2.882.265-4.185.75M12 20.25c1.472 0 2.882.265 4.185.75M18.75 4.97A48.416 48.416 0 0012 4.5c-2.291 0-4.545.16-6.75.47m13.5 0c1.01.143 2.01.317 3 .52m-3-.52c2.625.98 4.5 3.642 4.5 6.75v1.5c0 3.108-1.875 5.77-4.5 6.75m-16.5-6.75c-2.625-.98-4.5-3.642-4.5-6.75v-1.5c0-3.108 1.875-5.77 4.5-6.75m16.5 6.75c-1.01-.143-2.01-.317-3-.52m3 .52c-2.625-.98-4.5-3.642-4.5-6.75v-1.5c0-3.108 1.875-5.77 4.5-6.75M4.5 10.5A2.25 2.25 0 002.25 12v1.5c0 1.243 1.007 2.25 2.25 2.25M19.5 10.5a2.25 2.25 0 012.25 2.25v1.5c0 1.243-1.007 2.25-2.25 2.25" />
    </svg>
);

export const UsersIcon: React.FC<SVGProps> = (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-3.741-5.582M12 15.75a3 3 0 01-3-3V4.5a3 3 0 013-3h4.5a3 3 0 013 3v8.25a3 3 0 01-3 3H12zM6 18.72h8.25a9.094 9.094 0 013.741-.479 3 3 0 01-3.741-5.582M12 15.75a3 3 0 00-3-3V4.5a3 3 0 00-3-3H4.5a3 3 0 00-3 3v8.25a3 3 0 003 3H6" />
    </svg>
);

export const ShareIcon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M7.217 10.907a2.25 2.25 0 100 2.186m0-2.186c.195.025.39.044.585.057a4.505 4.505 0 014.396 4.396c-.013.195-.032.39-.057.585m0-4.982a2.25 2.25 0 100-2.186m0 2.186c-.195-.025-.39-.044-.585-.057a4.505 4.505 0 00-4.396-4.396c.013-.195.032-.39.057-.585m0 4.982a2.25 2.25 0 100 2.186m0-2.186c.195.025.39.044.585.057a4.505 4.505 0 014.396 4.396c-.013.195-.032.39-.057.585M16.5 12a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5m0 0c-.195.025-.39.044-.585.057a4.505 4.505 0 01-4.396 4.396c.013.195.032.39.057.585m0-4.982a2.25 2.25 0 100-2.186m0 2.186c-.195-.025-.39-.044-.585-.057a4.505 4.505 0 00-4.396-4.396c.013-.195.032-.39.057-.585" />
  </svg>
);

export const WalletIcon: React.FC<SVGProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M21 12V7.5a4.5 4.5 0 00-4.5-4.5H7.5A4.5 4.5 0 003 7.5v9A4.5 4.5 0 007.5 21h6.75M17.25 12a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5zm-1.5 8.25H12a.75.75 0 010-1.5h3.75a.75.75 0 010 1.5z" />
  </svg>
);

// Add other icons as needed based on constants.ts
// e.g., Twitter, Discord, Telegram specific icons if available or using generic ones.
    

import React from 'react';
import { WEB3_EDUCATION_TOPICS } from '../constants';
import { EducationTopicContent } from '../types';
import Card from '../components/ui/Card';

const EducationTopicCard: React.FC<{ topic: EducationTopicContent }> = ({ topic }) => (
  <Card className="p-6 flex flex-col" hoverEffect>
    <div className="flex items-start mb-4">
      {topic.icon && React.isValidElement(topic.icon) && (
        <div className="mr-4 text-billieGold pt-1">
          {React.cloneElement(topic.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-8 h-8" })}
        </div>
      )}
      <div className="flex-1">
        <h3 className="text-xl md:text-2xl font-bold text-billieGold mb-2">{topic.title}</h3>
      </div>
    </div>
    <p className="text-neutral-300 mb-3 text-sm md:text-base flex-grow">{topic.summary}</p>
    <details className="text-xs md:text-sm text-neutral-400">
        <summary className="cursor-pointer hover:text-billieAccent transition-colors">Learn more</summary>
        <p className="mt-2 bg-neutral-900/50 p-3 rounded-md">{topic.details}</p>
    </details>
  </Card>
);

const EducationPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12 md:mb-16">
        <h1 className="font-display text-4xl sm:text-5xl font-bold text-billieHeading mb-4">
          Billie's <span className="text-billiePurple">Web3 Academy</span>
        </h1>
        <p className="text-lg text-billieAccent max-w-2xl mx-auto">
          Learn the ins and outs of the decentralized world with Billie. Knowledge is power in the crypto space!
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6 md:gap-8">
        {WEB3_EDUCATION_TOPICS.map(topic => (
          <EducationTopicCard key={topic.title} topic={topic} />
        ))}
      </div>

      <div className="mt-16 text-center bg-neutral-800/80 backdrop-blur-sm p-8 rounded-xl shadow-xl border border-neutral-700">
        <h2 className="text-2xl font-display font-bold text-billieGold mb-4">Test Your Knowledge!</h2>
        <p className="text-neutral-300 mb-6">
          Billie is crafting some fun quizzes and interactive challenges to help you master Web3. Sharpen your wits and get ready! (Coming Soon)
        </p>
        {/* Placeholder for future gamification/quizzes */}
        <div className="animate-subtle-pulse text-billieAccent">
            Interactive quizzes launching soon...
        </div>
      </div>
    </div>
  );
};

export default EducationPage;
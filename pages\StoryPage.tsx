import React from 'react';
import { BILLIE_STORY_EVENTS, BILLIE_QUOTE } from '../constants';
import { StoryEvent } from '../types';
import Card from '../components/ui/Card';

const StoryEventCard: React.FC<{ event: StoryEvent; index: number }> = ({ event, index }) => {
  const isEven = index % 2 === 0;
  return (
    <div className={`flex md:contents ${isEven ? '' : 'flex-row-reverse'}`}>
      {!isEven && <div className="col-start-1 col-end-5 md:mx-auto relative mr-0 md:mr-auto"></div>}
      <div className={`col-start-${isEven ? '1' : '5'} col-end-${isEven ? '5' : '9'} md:mx-auto relative ${isEven ? 'mr-0 md:mr-auto' : 'ml-0 md:ml-auto'}`}>
        <div className={`h-full w-6 flex items-center justify-center absolute md:static ${isEven ? 'left-0 md:left-auto' : 'right-0 md:right-auto'}`}>
          <div className="h-full w-1 bg-billiePurple pointer-events-none"></div>
        </div>
        <div className={`w-6 h-6 absolute top-1/2 -mt-3 rounded-full shadow text-center ${isEven ? '-ml-0 md:-ml-[0.8rem]' : '-mr-0 md:-mr-[0.8rem] right-0 md:right-auto'} bg-billieGold flex items-center justify-center text-billieBlack font-bold`}>
          {event.icon ? React.cloneElement(event.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-4 h-4" }) : index + 1}
        </div>
        <Card className={`p-4 md:p-6 ${isEven ? 'ml-10 md:ml-0' : 'mr-10 md:mr-0'}`}>
          <p className="text-sm font-semibold text-billieAccent mb-1">{event.year}</p>
          <h3 className="text-xl font-bold text-billieGold mb-2">{event.title}</h3>
          <p className="text-neutral-300 text-sm">{event.description}</p>
        </Card>
      </div>
      {isEven && <div className="col-start-5 col-end-9 md:mx-auto relative ml-0 md:ml-auto"></div>}
    </div>
  );
};

const StoryPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12 md:mb-16">
        <h1 className="font-display text-4xl sm:text-5xl font-bold text-billieHeading mb-4">
          The Epic Saga of <span className="text-billieGold">Billie the Bear</span>
        </h1>
        <p className="text-lg text-billieAccent max-w-2xl mx-auto">
          Follow Billie's journey from a curious cub in the digital wilderness to a Web3 legend.
        </p>
         <blockquote className="italic text-xl text-billiePurple border-l-4 border-billieGold pl-4 py-2 my-8 max-w-xl mx-auto">
            "{BILLIE_QUOTE}"
          </blockquote>
      </div>

      <div className="flex flex-col md:grid grid-cols-9 mx-auto p-2 text-blue-50">
        {BILLIE_STORY_EVENTS.map((event, index) => (
          <StoryEventCard key={event.id} event={event} index={index} />
        ))}
      </div>
      
      <div className="mt-16 text-center">
        <p className="text-xl text-billieBodyText">Billie's story is still being written. Join the community to be part of the next chapter!</p>
      </div>
    </div>
  );
};

export default StoryPage;
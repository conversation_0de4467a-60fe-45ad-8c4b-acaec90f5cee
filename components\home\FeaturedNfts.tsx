
import React from 'react';
import { Link } from 'react-router-dom';
import { MOCK_NFTS } from '../../constants';
import { NFT } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { SparklesIcon, ChevronRightIcon } from '../ui/Icons';

const FeaturedNftCard: React.FC<{ nft: NFT }> = ({ nft }) => {
  return (
    <Card className="flex flex-col" hoverEffect>
      <img src={nft.imageUrl} alt={nft.name} className="w-full h-48 object-cover" />
      <div className="p-4 flex flex-col flex-grow">
        <h3 className="text-lg font-bold text-billieGold mb-1">{nft.name}</h3>
        {nft.rarity && <span className="text-xs bg-billiePurple text-white px-2 py-0.5 rounded-full self-start mb-2">{nft.rarity}</span>}
        <p className="text-sm text-neutral-300 mb-3 flex-grow">{nft.description.substring(0, 60)}...</p>
        {nft.price && <p className="text-md font-semibold text-billieAccent mb-3">{nft.price}</p>}
        <Button variant="secondary" size="sm" className="mt-auto w-full">View Details</Button>
      </div>
    </Card>
  );
};

const FeaturedNfts: React.FC = () => {
  const featured = MOCK_NFTS.slice(0, 3); // Show first 3 NFTs

  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12">
        <h2 className="font-display text-3xl sm:text-4xl font-bold text-billieHeading mb-4">
          Featured <span className="text-billiePurple">Billie NFTs</span>
        </h2>
        <p className="text-lg text-neutral-300 max-w-2xl mx-auto">
          Get your paws on exclusive Billie the Bear digital collectibles. Each NFT is a unique piece of the Billieverse.
        </p>
      </div>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
        {featured.map(nft => (
          <FeaturedNftCard key={nft.id} nft={nft} />
        ))}
      </div>
      <div className="text-center mt-12">
        <Link to="/nfts">
          <Button variant="primary" size="lg" rightIcon={<ChevronRightIcon className="w-5 h-5" />}>
            Explore Full Collection
          </Button>
        </Link>
      </div>
    </section>
  );
};

export default FeaturedNfts;
    
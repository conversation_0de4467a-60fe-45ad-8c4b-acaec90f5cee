
import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../ui/Button';
import { BILLIE_QUOTE } from '../../constants';
import { ChevronRightIcon } from '../ui/Icons';

const AboutSection: React.FC = () => {
  return (
    <section className="py-16 bg-neutral-900/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="font-display text-3xl sm:text-4xl font-bold text-billieGold mb-6">
            Who is <PERSON>?
          </h2>
          <img 
            src="https://picsum.photos/seed/billieprofile/150/150" 
            alt="Billie the Bear profile" 
            className="w-32 h-32 md:w-40 md:h-40 rounded-full mx-auto mb-6 border-4 border-billiePurple shadow-lg" 
          />
          <p className="text-lg text-billieBodyText mb-6">
            <PERSON> isn't your average forest dweller. Forged in the fires of volatile crypto markets and enlightened by the endless possibilities of blockchain, Billie is a Web3 evangelist with a taste for innovation (and FOMO tears).
          </p>
          <blockquote className="italic text-xl text-billieAccent border-l-4 border-billiePurple pl-4 py-2 my-8 max-w-xl mx-auto">
            "{BILLIE_QUOTE}"
          </blockquote>
          <p className="text-lg text-billieBodyText mb-8">
            From navigating the treacherous bear market to riding the exhilarating waves of the bull run, Billie has learned, adapted, and thrived. Now, he's here to share his journey and insights into the revolutionary world of Web3.
          </p>
          <Link to="/story">
            <Button variant="secondary" size="lg" rightIcon={<ChevronRightIcon className="w-5 h-5" />}>
              Discover Billie's Full Story
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
    
import React, { useState } from 'react';
import { MOCK_NFTS } from '../constants';
import { NFT } from '../types';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';
import { SparklesIcon, WalletIcon, ArrowPathIcon, XMarkIcon, LightBulbIcon } from '../components/ui/Icons'; // Added XMarkIcon and LightBulbIcon for status messages
// import WalletConnectButton from '../components/billie/WalletConnectButton'; // Not directly used for connection check here, modal has its own flow

const NftCard: React.FC<{ nft: NFT; onMintClick: (nft: NFT) => void }> = ({ nft, onMintClick }) => {
  return (
    <Card className="flex flex-col" hoverEffect>
      <img src={nft.imageUrl} alt={nft.name} className="w-full h-64 object-cover" />
      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-2xl font-bold text-billieGold mb-2">{nft.name}</h3>
        <div className="flex justify-between items-center mb-3">
            {nft.rarity && <span className="text-sm bg-billiePurple text-white px-3 py-1 rounded-full self-start">{nft.rarity}</span>}
            {nft.price && <p className="text-lg font-semibold text-billieAccent">{nft.price}</p>}
        </div>
        <p className="text-sm text-neutral-300 mb-4 flex-grow">{nft.description}</p>
        <p className="text-xs text-neutral-400 mb-1">Creator: {nft.creator || 'Billie Team'}</p>
        <Button variant="primary" onClick={() => onMintClick(nft)} className="w-full mt-4" leftIcon={<SparklesIcon className="w-5 h-5" />}>
          Mint This Billie
        </Button>
      </div>
    </Card>
  );
};

const NftPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedNft, setSelectedNft] = useState<NFT | null>(null);
  const [isMinting, setIsMinting] = useState(false);
  const [mintingStep, setMintingStep] = useState<'initial' | 'connecting' | 'minting' | 'success' | 'error'>('initial');
  const [mintingMessage, setMintingMessage] = useState('');

  // This would ideally come from a wallet context or prop
  const [isWalletConnected, setIsWalletConnected] = useState(false); 
  const [isConnectingWallet, setIsConnectingWallet] = useState(false);


  const handleMintClick = (nft: NFT) => {
    setSelectedNft(nft);
    setIsModalOpen(true);
    setMintingStep('initial'); // Reset status
    setMintingMessage('');
  };

  const handleConnectWalletInModal = async () => {
    setIsConnectingWallet(true);
    setMintingStep('connecting');
    setMintingMessage('Connecting your wallet...');
    // Simulate wallet connection
    await new Promise(resolve => setTimeout(resolve, 1500));
    // Simulate outcome
    if (Math.random() > 0.1) { // 90% success
        setIsWalletConnected(true);
        setMintingStep('initial'); // Back to initial state, ready to mint
        setMintingMessage('Wallet connected successfully! You can now mint.');
    } else {
        setIsWalletConnected(false);
        setMintingStep('error');
        setMintingMessage('Failed to connect wallet. Please try again or check your wallet provider.');
    }
    setIsConnectingWallet(false);
  }

  const confirmMint = async () => {
    if (!selectedNft || !isWalletConnected) return;

    setIsMinting(true);
    setMintingStep('minting');
    setMintingMessage(`Minting ${selectedNft.name}... This is a mock transaction. Please wait.`);
    
    await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate network delay

    if (Math.random() > 0.15) { // 85% success rate for mock
      setMintingStep('success');
      setMintingMessage(`Successfully minted ${selectedNft.name}! (Mock) It would typically appear in your wallet shortly.`);
    } else {
      setMintingStep('error');
      setMintingMessage(`Failed to mint ${selectedNft.name}. (Mock) This could be due to network issues or a rejected transaction. Please try again.`);
    }
    setIsMinting(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedNft(null);
    // Optionally reset minting step if desired when closing modal before completion
    // setMintingStep('initial'); 
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12 md:mb-16">
        <h1 className="font-display text-4xl sm:text-5xl font-bold text-billieHeading mb-4">
          Billie's <span className="text-billiePurple">NFT Collection</span>
        </h1>
        <p className="text-lg text-billieAccent max-w-2xl mx-auto">
          Own a piece of the Billieverse! Each NFT is a unique, collectible digital artwork. Mint yours today and join the elite club of Billie holders.
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {MOCK_NFTS.map(nft => (
          <NftCard key={nft.id} nft={nft} onMintClick={handleMintClick} />
        ))}
      </div>

      {selectedNft && (
        <Modal isOpen={isModalOpen} onClose={closeModal} title={`Mint: ${selectedNft.name}`} size="md">
          <div className="space-y-6 p-2">
            <img src={selectedNft.imageUrl} alt={selectedNft.name} className="w-full h-60 object-cover rounded-lg mb-4 shadow-lg"/>
            <p className="text-sm text-neutral-300">{selectedNft.description}</p>
            <div className="flex justify-between items-center text-lg">
              <span className="text-neutral-400">Price:</span>
              <span className="font-bold text-billieGold">{selectedNft.price || 'N/A'}</span>
            </div>
            
            {!isWalletConnected && mintingStep !== 'success' && (
                <>
                    <p className="text-center text-billieAccent text-sm">You need to connect your wallet to mint NFTs.</p>
                    <Button 
                        variant="primary" 
                        className="w-full" 
                        onClick={handleConnectWalletInModal}
                        disabled={isConnectingWallet}
                        leftIcon={isConnectingWallet ? <ArrowPathIcon className="w-5 h-5 animate-spin"/> : <WalletIcon className="w-5 h-5" />}
                    >
                        {isConnectingWallet ? 'Connecting...' : 'Connect Wallet'}
                    </Button>
                </>
            )}

            {(mintingStep === 'connecting' || mintingStep === 'minting' || mintingStep === 'error' || mintingStep === 'success' || (mintingStep === 'initial' && mintingMessage)) && (
                 <div className={`p-3 rounded-md text-sm ${
                    mintingStep === 'success' ? 'bg-green-500/20 text-green-300' : 
                    mintingStep === 'error' ? 'bg-red-500/20 text-red-300' : 
                    'bg-neutral-700/50 text-neutral-300'
                 } flex items-start space-x-2`}>
                    {mintingStep === 'success' && <SparklesIcon className="w-5 h-5 text-green-400 flex-shrink-0"/>}
                    {mintingStep === 'error' && <XMarkIcon className="w-5 h-5 text-red-400 flex-shrink-0"/>}
                    {(mintingStep === 'connecting' || mintingStep === 'minting') && <ArrowPathIcon className="w-5 h-5 animate-spin text-billiePurple flex-shrink-0"/>}
                    {mintingStep === 'initial' && mintingMessage && <LightBulbIcon className="w-5 h-5 text-billieAccent flex-shrink-0"/>}
                    <p>{mintingMessage}</p>
                 </div>
            )}

            {isWalletConnected && (mintingStep === 'initial' || mintingStep === 'error') && (
              <Button 
                variant="secondary" 
                className="w-full" 
                onClick={confirmMint} 
                disabled={isMinting}
                leftIcon={isMinting ? <ArrowPathIcon className="w-5 h-5 animate-spin" /> : <SparklesIcon className="w-5 h-5" />}
              >
                {isMinting ? `Minting ${selectedNft.name}...` : `Confirm Mint ${selectedNft.name}`}
              </Button>
            )}
            
            {mintingStep === 'success' && (
                 <Button variant="outline" className="w-full" onClick={closeModal}>Close</Button>
            )}

            <p className="text-xs text-neutral-500 text-center pt-2">
              This is a simulated NFT minting process. No real transactions will occur.
            </p>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default NftPage;

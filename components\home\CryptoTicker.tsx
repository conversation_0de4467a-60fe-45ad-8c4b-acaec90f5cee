
import React, { useState, useEffect } from 'react';
import { MOCK_CRYPTO_PRICES } from '../../constants';
import { CryptoPrice } from '../../types';

const CryptoTicker: React.FC = () => {
  const [prices, setPrices] = useState<CryptoPrice[]>(MOCK_CRYPTO_PRICES);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
useEffect(() => {
    // In a real app, you'd fetch this data from an API
    // and update it periodically. For this mock, we'll just
    // slightly vary the prices to show change.
    const interval = setInterval(() => {
      setPrices(prevPrices =>
        prevPrices.map(coin => {
          const priceNum = parseFloat(coin.price.replace('$', '').replace(',', ''));
          const change = (Math.random() - 0.5) * (priceNum * 0.01); // +/- 1% change
          const newPrice = priceNum + change;
          const change24hNum = parseFloat(coin.change24h.replace('%', ''));
          const newChange24h = change24hNum + (Math.random() - 0.5) * 0.1; // Fluctuate change %

          return {
            ...coin,
            price: `$${newPrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
            change24h: `${newChange24h >= 0 ? '+' : ''}${newChange24h.toFixed(1)}%`,
          };
        })
      );
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);


  return (
    <section className="bg-neutral-800/50 py-4 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative flex">
          <div className="animate-marquee flex space-x-8">
            {prices.concat(prices).map((coin, index) => ( // Duplicate for seamless loop
              <div key={`${coin.id}-${index}`} className="flex items-center space-x-3 min-w-max p-2 rounded-lg hover:bg-neutral-700/50 transition-colors">
                {coin.iconUrl && <img src={coin.iconUrl} alt={coin.name} className="w-6 h-6 rounded-full" />}
                <div>
                  <span className="font-semibold text-sm text-billieHeading">{coin.symbol}</span>
                  <span className="text-sm text-billieBodyText ml-2">{coin.price}</span>
                </div>
                <span className={`text-xs font-medium ml-1 ${coin.change24h.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                  {coin.change24h}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
      <style>{`
        @keyframes marquee {
          0% { transform: translateX(0%); }
          100% { transform: translateX(-50%); }
        }
        .animate-marquee {
          animation: marquee 40s linear infinite;
        }
      `}</style>
    </section>
  );
};

export default CryptoTicker;

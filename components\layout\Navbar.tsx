
import React, { useState } from 'react';
import { Link, NavLink } from 'react-router-dom';
import { NAV_ITEMS } from '../../constants';
import { Bars3Icon, XMarkIcon } from '../ui/Icons'; // Assuming Icons.tsx
import WalletConnectButton from '../billie/WalletConnectButton';

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="sticky top-0 z-50 bg-billieBlack/80 backdrop-blur-md shadow-lg">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0">
              <div className="flex items-center space-x-2">
                {/* Placeholder for <PERSON>'s Logo/Icon */}
                <img className="h-10 w-10 rounded-full bg-billieGold p-1" src="https://picsum.photos/seed/billielogo/40/40" alt="Billie Logo" />
                <span className="font-display text-2xl font-bold text-billieGold">BILLIE</span>
              </div>
            </Link>
          </div>
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {NAV_ITEMS.map((item) => (
                <NavLink
                  key={item.name}
                  to={item.path}
                  className={({ isActive }) =>
                    `px-3 py-2 rounded-md text-sm font-medium ${
                      isActive
                        ? 'bg-billiePurple text-white'
                        : 'text-billieBodyText hover:bg-neutral-700 hover:text-white'
                    } transition-colors duration-150`
                  }
                >
                  {item.name}
                </NavLink>
              ))}
            </div>
          </div>
          <div className="hidden md:block ml-4">
            <WalletConnectButton />
          </div>
          <div className="-mr-2 flex md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              type="button"
              className="bg-neutral-800 inline-flex items-center justify-center p-2 rounded-md text-billieBodyText hover:text-white hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-800 focus:ring-white"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {!isOpen ? (
                <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {NAV_ITEMS.map((item) => (
              <NavLink
                key={item.name}
                to={item.path}
                onClick={() => setIsOpen(false)}
                className={({ isActive }) =>
                  `block px-3 py-2 rounded-md text-base font-medium ${
                    isActive
                      ? 'bg-billiePurple text-white'
                      : 'text-billieBodyText hover:bg-neutral-700 hover:text-white'
                  } transition-colors duration-150`
                }
              >
                {item.name}
              </NavLink>
            ))}
          </div>
          <div className="pt-4 pb-3 border-t border-neutral-700 px-5">
             <WalletConnectButton />
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
    
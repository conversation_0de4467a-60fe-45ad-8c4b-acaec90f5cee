
import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../ui/Button';
import { UsersIcon, BookOpenIcon, SparklesIcon } from '../ui/Icons';

const CtaSection: React.FC = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="bg-gradient-to-r from-billiePurple via-purple-700 to-billieGold p-8 md:p-12 rounded-xl shadow-2xl text-center">
        <h2 className="font-display text-3xl sm:text-4xl font-bold text-white mb-6">
          Ready to Dive Deeper into Web3?
        </h2>
        <p className="text-lg text-purple-100 mb-10 max-w-2xl mx-auto">
          Whether you're a seasoned crypto degen or just Web3-curious, Billie's world has something for you. Explore, learn, and become part of the community.
        </p>
        <div className="flex flex-col sm:flex-row flex-wrap justify-center gap-4">
          <Link to="/nfts">
            <Button variant="outline" size="lg" className="bg-white text-billiePurple hover:bg-purple-100 border-white hover:border-purple-100" leftIcon={<SparklesIcon className="w-5 h-5" />}>
              Explore NFTs
            </Button>
          </Link>
          <Link to="/education">
            <Button variant="outline" size="lg" className="bg-white text-billiePurple hover:bg-purple-100 border-white hover:border-purple-100" leftIcon={<BookOpenIcon className="w-5 h-5" />}>
              Web3 Academy
            </Button>
          </Link>
          <Link to="/community">
            <Button variant="outline" size="lg" className="bg-white text-billiePurple hover:bg-purple-100 border-white hover:border-purple-100" leftIcon={<UsersIcon className="w-5 h-5" />}>
              Join Community
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
    
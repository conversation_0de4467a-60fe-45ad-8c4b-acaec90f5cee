
import React from 'react';
import { SOCIAL_LINKS } from '../constants';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const CommunityPage: React.FC = () => {
  const discordLink = SOCIAL_LINKS.find(link => link.name === 'Discord');
  const twitterLink = SOCIAL_LINKS.find(link => link.name === 'Twitter');
  const telegramLink = SOCIAL_LINKS.find(link => link.name === 'Telegram');

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12 md:mb-16">
        <h1 className="font-display text-4xl sm:text-5xl font-bold text-billieHeading mb-4">
          Join the <span className="text-billieGold">Billieverse</span> Community
        </h1>
        <p className="text-lg text-billieAccent max-w-2xl mx-auto">
          Connect with fellow Web3 enthusiasts, share insights, participate in events, and be part of <PERSON>'s growing family.
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {discordLink && (
          <Card className="p-6 md:p-8 text-center flex flex-col items-center" hoverEffect>
            {React.isValidElement(discordLink.icon) && React.cloneElement(discordLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-billiePurple mb-4" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3">Billie's Den (Discord)</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              The main hub for all things Billie. Chat, share alpha, participate in events, and get direct updates.
            </p>
            <a href={discordLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <Button variant="primary" size="lg">Join Discord</Button>
            </a>
          </Card>
        )}
        
        {twitterLink && (
          <Card className="p-6 md:p-8 text-center flex flex-col items-center" hoverEffect>
             {React.isValidElement(twitterLink.icon) && React.cloneElement(twitterLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-billiePurple mb-4" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3">Follow on X (Twitter)</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              Get the latest news, memes, and Billie's hot takes on the crypto market. Don't miss out!
            </p>
            <a href={twitterLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <Button variant="secondary" size="lg">Follow on X</Button>
            </a>
          </Card>
        )}

        {telegramLink && (
           <Card className="p-6 md:p-8 text-center flex flex-col items-center" hoverEffect>
            {React.isValidElement(telegramLink.icon) && React.cloneElement(telegramLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-billiePurple mb-4" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3">Telegram Updates</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              Subscribe to our Telegram channel for important announcements and community news.
            </p>
            <a href={telegramLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <Button variant="outline" size="lg">Join Telegram</Button>
            </a>
          </Card>
        )}
      </div>
      
      <Card className="p-8 bg-neutral-800/80 backdrop-blur-sm border border-neutral-700">
        <h2 className="text-3xl font-display font-bold text-billieHeading mb-6 text-center">
            Share Your <span className="text-billieGold">Creations!</span>
        </h2>
        <div className="text-center">
            <p className="text-neutral-300 mb-4 text-lg">
                Got fan art, cool memes, or ideas for Billie? We'd love to see them! 
                Share your work in the dedicated channels on our Discord server.
            </p>
            <p className="text-billieAccent mb-6">
                The best creations might get featured on the website or Billie's social media!
            </p>
            {discordLink && (
                <a href={discordLink.url} target="_blank" rel="noopener noreferrer">
                    <Button variant='primary' size="lg" >Show Your Talent!</Button>
                </a>
            )}
        </div>
      </Card>

      <div className="mt-16 text-center">
        <h3 className="text-2xl font-bold text-billieHeading mb-3">User-Generated Content Spotlight</h3>
        <p className="text-neutral-300 mb-6 max-w-xl mx-auto">
          Billie loves to see your creativity! Soon, we'll feature amazing fan art and memes right here. Keep an eye on this space.
        </p>
        <div className="bg-neutral-800/50 p-6 rounded-lg animate-subtle-pulse">
            <p className="text-billieGold text-lg">Spotlight Gallery Coming Soon!</p>
        </div>
      </div>
    </div>
  );
};

export default CommunityPage;

import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverEffect?: boolean;
}

const Card: React.FC<CardProps> = ({ children, className, onClick, hoverEffect = false }) => {
  const baseClasses = "bg-neutral-800/70 backdrop-blur-sm rounded-xl shadow-xl overflow-hidden border border-neutral-700";
  const hoverClasses = hoverEffect ? "transition-all duration-300 ease-in-out hover:shadow-billiePurple/30 hover:scale-[1.02] hover:border-billiePurple/50" : "";
  
  return (
    <div
      className={`${baseClasses} ${hoverClasses} ${className || ''}`}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => (e.key === 'Enter' || e.key === ' ') && onClick() : undefined}
    >
      {children}
    </div>
  );
};

export default Card;
    
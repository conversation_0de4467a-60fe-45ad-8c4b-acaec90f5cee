
export interface NavItem {
  name: string;
  path: string;
}

export interface NFT {
  id: string;
  name: string;
  imageUrl: string;
  price?: string; // e.g., "0.5 ETH"
  creator?: string;
  rarity?: string;
  description: string;
}

export interface StoryEvent {
  id: string;
  year: string;
  title: string;
  description: string;
  icon?: React.ReactNode; // For timeline
}

export interface EducationTopicContent {
  title: string;
  summary: string;
  details: string; // Can include markdown or HTML
  icon?: React.ReactNode;
}

export interface CryptoPrice {
  id: string;
  name: string;
  symbol: string;
  price: string;
  change24h: string; // e.g., "+2.5%" or "-1.0%"
  iconUrl?: string;
}

export interface SocialLink {
  name: string;
  url: string;
  icon: React.ReactNode;
}

export interface BlockchainData {
  blockNumber: number;
  gasPrice: string; // e.g., "25 Gwei"
}
    
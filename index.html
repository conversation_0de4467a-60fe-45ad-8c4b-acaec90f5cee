
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" /> <!-- Replace with actual favicon -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Billie the Bear - Web3 Adventure</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              billiePurple: '#7C3AED', // purple-600
              billieGold: '#FBBF24',   // amber-400
              billieBlack: '#171717',  // neutral-900 (suit, dark theme background)
              billieRed: '#EF4444',    // red-600
              billieBodyText: '#E2E8F0', // slate-200
              billieHeading: '#FFFFFF',
              billieAccent: '#A78BFA', // violet-400
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'], // Example: Using Inter
              display: ['Orbitron', 'sans-serif'], // Example: Playful, bold font for titles
            },
            animation: {
              'subtle-pulse': 'subtlePulse 2s infinite ease-in-out',
              'float': 'float 6s ease-in-out infinite',
            },
            keyframes: {
              subtlePulse: {
                '0%, 100%': { opacity: '1' },
                '50%': { opacity: '.7' },
              },
              float: {
                '0%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' },
                '100%': { transform: 'translateY(0px)' },
              }
            }
          }
        }
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&family=Orbitron:wght@700;900&display=swap" rel="stylesheet">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-billieBlack text-billieBodyText font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
    
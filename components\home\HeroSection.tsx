
import React from 'react';
import Billie3DModelPlaceholder from '../billie/Billie3DModelPlaceholder';
import Button from '../ui/Button';
import { BILLIE_TAGLINE } from '../../constants';
import { Link } from 'react-router-dom';
import { SparklesIcon } from '../ui/Icons';

const HeroSection: React.FC = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-24">
      <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">
        <div className="text-center md:text-left">
          <h1 className="font-display text-4xl sm:text-5xl lg:text-6xl font-black text-billieHeading mb-6 leading-tight">
            Meet <span className="text-billieGold">Billie</span> The Bear
          </h1>
          <p className="text-xl sm:text-2xl text-billieAccent mb-8 font-semibold">
            {BILLIE_TAGLINE}
          </p>
          <p className="text-neutral-300 mb-10 text-lg">
            Dive into the world of Web3 with your guide through the crypto cosmos. Billie's seen it all, from bear market despair to bull run euphoria. Ready to join the adventure?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
            <Link to="/nfts">
              <Button variant="primary" size="lg" leftIcon={<SparklesIcon className="w-5 h-5" />}>
                Mint Billie NFT
              </Button>
            </Link>
            <Link to="/story">
              <Button variant="outline" size="lg">
                Billie's Story
              </Button>
            </Link>
          </div>
        </div>
        <div className="mt-12 md:mt-0">
          <Billie3DModelPlaceholder />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
    